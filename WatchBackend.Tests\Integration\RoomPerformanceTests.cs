using FluentAssertions;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Moq;
using System.Diagnostics;
using WatchBackend.Core.Interfaces;
using WatchBackend.Infrastructure.Services;
using Xunit;
using Xunit.Abstractions;

namespace WatchBackend.Tests.Integration
{
    public class RoomPerformanceTests : IDisposable
    {
        private readonly ITestOutputHelper _output;

        public RoomPerformanceTests(ITestOutputHelper output)
        {
            _output = output;
        }

        [Fact]
        public async Task DebounceService_ShouldBatchDatabaseWrites()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<DebounceService>>();
            var debounceService = new DebounceService(loggerMock.Object);
            var writeCount = 0;

            // Act - Schedule multiple operations rapidly
            for (int i = 0; i < 10; i++)
            {
                debounceService.DebounceAsync($"test-{i % 3}", () =>
                {
                    Interlocked.Increment(ref writeCount);
                    return Task.CompletedTask;
                }, TimeSpan.FromMilliseconds(100));
            }

            // Wait for debounce to complete
            await Task.Delay(200);

            // Assert - Should only execute 3 operations (one per unique key)
            writeCount.Should().Be(3);

            _output.WriteLine($"Scheduled 10 operations with 3 unique keys");
            _output.WriteLine($"Actual executions: {writeCount}");
            _output.WriteLine("✅ Debouncing reduced operations by 70%");
        }

        [Fact]
        public async Task DebounceService_ShouldHandleConcurrentOperations()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<DebounceService>>();
            var debounceService = new DebounceService(loggerMock.Object);
            var writeCount = 0;
            var stopwatch = Stopwatch.StartNew();

            // Act - Simulate concurrent operations from multiple "users"
            var tasks = new List<Task>();
            for (int user = 0; user < 5; user++)
            {
                for (int operation = 0; operation < 10; operation++)
                {
                    var roomKey = $"room_{user % 2}"; // 2 rooms, 5 users
                    tasks.Add(Task.Run(() =>
                    {
                        debounceService.DebounceAsync(roomKey, () =>
                        {
                            Interlocked.Increment(ref writeCount);
                            return Task.CompletedTask;
                        }, TimeSpan.FromMilliseconds(100));
                    }));
                }
            }

            await Task.WhenAll(tasks);
            stopwatch.Stop();

            // Wait for debounce to complete
            await Task.Delay(200);

            // Assert
            _output.WriteLine($"50 operations from 5 users across 2 rooms");
            _output.WriteLine($"Completed in: {stopwatch.ElapsedMilliseconds}ms");
            _output.WriteLine($"Database writes: {writeCount}");

            // Should only have 2 writes (one per room)
            writeCount.Should().Be(2);
            _output.WriteLine("✅ Reduced 50 operations to 2 database writes (96% reduction)");
        }

        [Fact]
        public async Task DebounceService_ShouldFlushOperationsImmediately()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<DebounceService>>();
            var debounceService = new DebounceService(loggerMock.Object);
            var executed = false;

            // Act
            debounceService.DebounceAsync("test-key", () =>
            {
                executed = true;
                return Task.CompletedTask;
            }, TimeSpan.FromSeconds(10)); // Long delay

            // Flush immediately
            await debounceService.FlushAsync("test-key");

            // Assert
            executed.Should().BeTrue();
            _output.WriteLine("✅ Flush executed operation immediately despite long delay");
        }

        public void Dispose()
        {
            // Cleanup if needed
        }
    }
}

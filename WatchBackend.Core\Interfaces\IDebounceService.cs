namespace WatchBackend.Core.Interfaces
{
    /// <summary>
    /// Service for debouncing operations to prevent excessive execution of expensive operations
    /// </summary>
    public interface IDebounceService
    {
        /// <summary>
        /// Debounces an async operation by key. If called multiple times with the same key,
        /// only the last operation will be executed after the specified delay.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute</param>
        /// <param name="delay">Delay before executing the operation</param>
        void DebounceAsync(string key, Func<Task> operation, TimeSpan delay);

        /// <summary>
        /// Debounces an async operation by key with a default delay.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        /// <param name="operation">The async operation to execute</param>
        void DebounceAsync(string key, Func<Task> operation);

        /// <summary>
        /// Immediately executes any pending operation for the given key and cancels the debounce timer.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        Task FlushAsync(string key);

        /// <summary>
        /// Cancels any pending operation for the given key without executing it.
        /// </summary>
        /// <param name="key">Unique identifier for the operation</param>
        void Cancel(string key);

        /// <summary>
        /// Cancels all pending operations without executing them.
        /// </summary>
        void CancelAll();
    }
}

using FluentResults;
using Mapster;
using System.Collections.Concurrent;
using WatchBackend.Core.Entities;
using WatchBackend.Core.Errors;
using WatchBackend.Core.Extensions;
using WatchBackend.Core.Interfaces;
using WatchBackend.Core.Models;
using WatchBackend.Core.Queries;
using WatchBackend.Infrastructure.Interfaces;

namespace WatchBackend.Infrastructure.Services
{
    internal class RoomService : IRoomService
    {
        private readonly IIdHashService _idHashService;
        private readonly IRoomCache _roomCache;
        private readonly IRoomHubService _roomHubService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly ConcurrentDictionary<int, Timer> _roomSaveTimers = new();
        private readonly TimeSpan _saveDebounceDelay = TimeSpan.FromSeconds(2);

        public RoomService(
            IUnitOfWork unitOfWork,
            IIdHashService idHashService,
            IRoomCache roomCache,
            IRoomHubService roomHubService
        )
        {
            _idHashService = idHashService;
            _roomCache = roomCache;
            _roomHubService = roomHubService;
            _unitOfWork = unitOfWork;
        }

        public async Task<Room> CreateRoomAsync(User creatorUser)
        {
            var roomUser = CreateRoomUser(creatorUser, RoomUserRoles.Creator);
            var roomUserEntity = roomUser.Adapt<RoomUserEntity>();

            var room = new Room();
            var roomEntity = room.Adapt<RoomEntity>();

            roomEntity.Users.Add(roomUserEntity);

            await InsertRoomAsync(roomEntity);

            roomUser.Id = roomUserEntity.Id;
            room.Id = roomEntity.Id;
            room.Playlist.Id = roomEntity.Playlist.Id;
            room.IdHash = _idHashService.IdToHash(room.Id);

            room.AddUser(roomUser);

            _roomCache.TryAdd(room.Id, room);

            return room;
        }

        public async Task SaveRoomAsync(Room room)
        {
            await SaveRoomImmediateAsync(room);
        }

        public void SaveRoomDebounced(Room room)
        {
            // Cancel any existing timer for this room
            if (_roomSaveTimers.TryGetValue(room.Id, out var existingTimer))
            {
                existingTimer.Dispose();
            }

            // Create a new timer that will save the room after the debounce delay
            var timer = new Timer(async _ =>
            {
                try
                {
                    await SaveRoomImmediateAsync(room);
                }
                catch (Exception ex)
                {
                    // Log the exception but don't throw to avoid crashing the timer
                    Console.WriteLine($"Error saving room {room.Id}: {ex.Message}");
                }
                finally
                {
                    // Remove the timer from the dictionary
                    _roomSaveTimers.TryRemove(room.Id, out var _);
                }
            }, null, _saveDebounceDelay, Timeout.InfiniteTimeSpan);

            _roomSaveTimers.AddOrUpdate(room.Id, timer, (_, oldTimer) =>
            {
                oldTimer.Dispose();
                return timer;
            });
        }

        private async Task SaveRoomImmediateAsync(Room room)
        {
            var roomEntity = room.Adapt<RoomEntity>();
            await UpdateRoomAsync(roomEntity);
        }

        public async Task<Result<Room>> GetRoomByIdAsync(int id)
        {
            if (_roomCache.TryGetValue(id, out var cachedRoom))
            {
                return Result.Ok(cachedRoom);
            }

            var roomQuery = new RoomQuery { IncludeRoomUsers = true, IncludeRoomPlaylist = true };
            var roomEntity = await _unitOfWork.Rooms.GetByIdAsync(id, roomQuery);
            if (roomEntity is null)
            {
                return Result.Fail(new NotFoundError("Room not found"));
            }

            var room = roomEntity.Adapt<Room>();

            _roomCache.TryAdd(room.Id, room);

            return Result.Ok(room);
        }

        public Task<Result<Room>> GetRoomByIdHashAsync(string idHash)
        {
            var idResult = _idHashService.HashToId(idHash);
            if (idResult.IsFailed)
            {
                return Task.FromResult(idResult.ToResult<Room>());
            }

            var id = idResult.Value;
            return GetRoomByIdAsync(id);
        }

        public async Task AddUserToRoomAsync(
            Room room,
            User user,
            RoomUserRoles role = RoomUserRoles.Viewer
        )
        {
            var roomUserResult = room.GetUserByUserId(user.Id);
            if (roomUserResult.IsSuccess)
            {
                return;
            }

            var roomEntity = await _unitOfWork.Rooms.GetByIdAsync(room.Id);
            if (roomEntity == null)
            {
                throw new ArgumentNullException($"Unexpected: room (id: ${room.Id}) not found");
            }

            var roomUser = CreateRoomUser(user, role);
            var roomUserEntity = roomUser.Adapt<RoomUserEntity>();

            roomEntity.Users.Add(roomUserEntity);

            await UpdateRoomAsync(roomEntity);

            roomUser.Id = roomUserEntity.Id;

            room.AddUser(roomUser);
        }

        public async Task AddSourceToPlaylist(
            Room room,
            SourceContainer source,
            bool setAsCurrentSource = false
        )
        {
            // Add to in-memory playlist
            room.Playlist.Add(source);
            if (setAsCurrentSource)
            {
                var sourceContainerIdx = room.Playlist.IndexOf(source);
                room.Playlist.SetCurrentIndex(sourceContainerIdx);
                room.StopSource();
            }

            // Create entity and ensure playlist is properly mapped
            var roomEntity = room.Adapt<RoomEntity>();

            // Add the source to the playlist entity
            var sourceEntity = source.Adapt<SourceContainerEntity>();
            roomEntity.Playlist.Items.Add(sourceEntity);

            // Update the database
            await UpdateRoomAsync(roomEntity);
        }

        public IEnumerable<Room> GetUserConnectedRooms(User user)
        {
            return _roomCache.Values.Where(r => r.GetAllConnectedUsers().Any(u => u.Id == user.Id));
        }

        public void BroadcastUserChangedToConnectedRooms(User user)
        {
            var userConnectedRooms = GetUserConnectedRooms(user);
            if (userConnectedRooms == null)
            {
                return;
            }

            foreach (var room in userConnectedRooms)
            {
                var roomUserResult = room.GetUserByUserId(user.Id);
                if (roomUserResult.IsFailed)
                {
                    continue;
                }

                var roomUser = roomUserResult.Value;
                user.Adapt(roomUser);
                //roomUser.Username = user.Username;

                SaveRoomAsync(room).RunAsync();
                _roomHubService.BroadcastRoomUserChanged(roomUser, room).RunAsync();
            }
        }

        private static RoomUser CreateRoomUser(User user, RoomUserRoles role = RoomUserRoles.Guest)
        {
            var roomUser = user.Adapt<RoomUser>();
            roomUser.Role = role;

            return roomUser;
        }

        private async Task InsertRoomAsync(RoomEntity roomEntity)
        {
            _unitOfWork.Rooms.Add(roomEntity);
            await _unitOfWork.CompleteAsync();
        }

        private async Task UpdateRoomAsync(RoomEntity roomEntity)
        {
            _unitOfWork.Rooms.Update(roomEntity);
            await _unitOfWork.CompleteAsync();
        }
    }
}

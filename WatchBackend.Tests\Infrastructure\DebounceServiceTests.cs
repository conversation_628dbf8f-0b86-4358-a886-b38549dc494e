using FluentAssertions;
using Microsoft.Extensions.Logging;
using Moq;
using WatchBackend.Infrastructure.Services;
using Xunit;

namespace WatchBackend.Tests.Infrastructure
{
    public class DebounceServiceTests : IDisposable
    {
        private readonly DebounceService _debounceService;
        private readonly Mock<ILogger<DebounceService>> _loggerMock;

        public DebounceServiceTests()
        {
            _loggerMock = new Mock<ILogger<DebounceService>>();
            _debounceService = new DebounceService(_loggerMock.Object);
        }

        [Fact]
        public async Task DebounceAsync_ShouldExecuteOperationAfterDelay()
        {
            // Arrange
            var executed = false;
            var operation = () =>
            {
                executed = true;
                return Task.CompletedTask;
            };

            // Act
            _debounceService.DebounceAsync("test-key", operation, TimeSpan.FromMilliseconds(100));

            // Assert - should not execute immediately
            executed.Should().BeFalse();

            // Wait for debounce delay
            await Task.Delay(150);
            executed.Should().BeTrue();
        }

        [Fact]
        public async Task DebounceAsync_ShouldCancelPreviousOperationWhenCalledMultipleTimes()
        {
            // Arrange
            var firstExecuted = false;
            var secondExecuted = false;

            var firstOperation = () =>
            {
                firstExecuted = true;
                return Task.CompletedTask;
            };

            var secondOperation = () =>
            {
                secondExecuted = true;
                return Task.CompletedTask;
            };

            // Act
            _debounceService.DebounceAsync("test-key", firstOperation, TimeSpan.FromMilliseconds(100));
            await Task.Delay(50); // Wait half the delay
            _debounceService.DebounceAsync("test-key", secondOperation, TimeSpan.FromMilliseconds(100));

            // Assert - first operation should be cancelled
            await Task.Delay(150);
            firstExecuted.Should().BeFalse();
            secondExecuted.Should().BeTrue();
        }

        [Fact]
        public async Task DebounceAsync_ShouldAllowMultipleKeysToExecuteConcurrently()
        {
            // Arrange
            var operation1Executed = false;
            var operation2Executed = false;

            var operation1 = () =>
            {
                operation1Executed = true;
                return Task.CompletedTask;
            };

            var operation2 = () =>
            {
                operation2Executed = true;
                return Task.CompletedTask;
            };

            // Act
            _debounceService.DebounceAsync("key1", operation1, TimeSpan.FromMilliseconds(100));
            _debounceService.DebounceAsync("key2", operation2, TimeSpan.FromMilliseconds(100));

            // Assert
            await Task.Delay(150);
            operation1Executed.Should().BeTrue();
            operation2Executed.Should().BeTrue();
        }

        [Fact]
        public async Task FlushAsync_ShouldExecuteOperationImmediately()
        {
            // Arrange
            var executed = false;
            var operation = () =>
            {
                executed = true;
                return Task.CompletedTask;
            };

            // Act
            _debounceService.DebounceAsync("test-key", operation, TimeSpan.FromSeconds(10)); // Long delay
            await _debounceService.FlushAsync("test-key");

            // Assert
            executed.Should().BeTrue();
        }

        [Fact]
        public async Task Cancel_ShouldPreventOperationExecution()
        {
            // Arrange
            var executed = false;
            var operation = () =>
            {
                executed = true;
                return Task.CompletedTask;
            };

            // Act
            _debounceService.DebounceAsync("test-key", operation, TimeSpan.FromMilliseconds(100));
            _debounceService.Cancel("test-key");

            // Assert
            await Task.Delay(150);
            executed.Should().BeFalse();
        }

        [Fact]
        public async Task DebounceAsync_ShouldHandleExceptionsGracefully()
        {
            // Arrange
            Func<Task> operation = () => throw new InvalidOperationException("Test exception");

            // Act & Assert - should not throw
            _debounceService.DebounceAsync("test-key", operation, TimeSpan.FromMilliseconds(50));
            await Task.Delay(100);

            // Verify exception was logged (basic verification that logging was called)
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        public void Dispose()
        {
            _debounceService.Dispose();
        }
    }
}

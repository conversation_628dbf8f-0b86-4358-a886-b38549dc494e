<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net7.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.NET.Test.Sdk" Version="17.8.0" />
    <PackageReference Include="xunit" Version="2.6.1" />
    <PackageReference Include="xunit.runner.visualstudio" Version="2.4.5">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="coverlet.collector" Version="6.0.0">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="7.0.14" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.InMemory" Version="7.0.20" />
    <PackageReference Include="Microsoft.AspNetCore.SignalR.Client" Version="7.0.14" />
    <PackageReference Include="FluentAssertions" Version="6.12.0" />
    <PackageReference Include="Moq" Version="4.20.69" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\WatchBackend.Web\WatchBackend.Web.csproj" />
    <ProjectReference Include="..\WatchBackend.Core\WatchBackend.Core.csproj" />
    <ProjectReference Include="..\WatchBackend.Infrastructure\WatchBackend.Infrastructure.csproj" />
    <ProjectReference Include="..\WatchBackend.Data\WatchBackend.Data.csproj" />
  </ItemGroup>

</Project>

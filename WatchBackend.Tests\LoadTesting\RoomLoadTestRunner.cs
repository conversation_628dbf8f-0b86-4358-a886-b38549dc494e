using Microsoft.AspNetCore.SignalR.Client;
using System.Collections.Concurrent;
using System.Diagnostics;
using Xunit.Abstractions;

namespace WatchBackend.Tests.LoadTesting
{
    public class RoomLoadTestRunner
    {
        private readonly string _baseUrl;
        private readonly ITestOutputHelper _output;

        public RoomLoadTestRunner(string baseUrl, ITestOutputHelper output)
        {
            _baseUrl = baseUrl;
            _output = output;
        }

        public async Task<LoadTestResult> RunLoadTest(LoadTestConfig config)
        {
            var result = new LoadTestResult
            {
                Config = config,
                StartTime = DateTime.UtcNow
            };

            var connections = new List<HubConnection>();
            var errors = new ConcurrentBag<Exception>();
            var operationTimes = new ConcurrentBag<TimeSpan>();

            try
            {
                // Create connections
                _output.WriteLine($"Creating {config.ConcurrentUsers} connections...");
                for (int i = 0; i < config.ConcurrentUsers; i++)
                {
                    var connection = await CreateConnection(config.RoomId);
                    connections.Add(connection);
                }

                _output.WriteLine($"Starting load test with {config.OperationsPerUser} operations per user...");
                var stopwatch = Stopwatch.StartNew();

                // Execute operations
                var tasks = connections.Select(async (connection, userIndex) =>
                {
                    for (int opIndex = 0; opIndex < config.OperationsPerUser; opIndex++)
                    {
                        try
                        {
                            var operationStopwatch = Stopwatch.StartNew();
                            
                            await ExecuteRandomOperation(connection, config.RoomId, userIndex, opIndex);
                            
                            operationStopwatch.Stop();
                            operationTimes.Add(operationStopwatch.Elapsed);

                            // Add delay between operations if configured
                            if (config.DelayBetweenOperationsMs > 0)
                            {
                                await Task.Delay(config.DelayBetweenOperationsMs);
                            }
                        }
                        catch (Exception ex)
                        {
                            errors.Add(ex);
                        }
                    }
                }).ToArray();

                await Task.WhenAll(tasks);
                stopwatch.Stop();

                result.EndTime = DateTime.UtcNow;
                result.TotalDuration = stopwatch.Elapsed;
                result.TotalOperations = config.ConcurrentUsers * config.OperationsPerUser;
                result.SuccessfulOperations = result.TotalOperations - errors.Count;
                result.Errors = errors.ToList();
                result.OperationTimes = operationTimes.ToList();

                // Calculate statistics
                if (operationTimes.Any())
                {
                    var sortedTimes = operationTimes.OrderBy(t => t.TotalMilliseconds).ToList();
                    result.AverageOperationTime = TimeSpan.FromMilliseconds(sortedTimes.Average(t => t.TotalMilliseconds));
                    result.MedianOperationTime = sortedTimes[sortedTimes.Count / 2];
                    result.P95OperationTime = sortedTimes[(int)(sortedTimes.Count * 0.95)];
                    result.MaxOperationTime = sortedTimes.Last();
                }

                result.OperationsPerSecond = result.SuccessfulOperations / result.TotalDuration.TotalSeconds;

                _output.WriteLine($"Load test completed:");
                _output.WriteLine($"  Total operations: {result.TotalOperations}");
                _output.WriteLine($"  Successful: {result.SuccessfulOperations}");
                _output.WriteLine($"  Errors: {result.Errors.Count}");
                _output.WriteLine($"  Duration: {result.TotalDuration.TotalSeconds:F2}s");
                _output.WriteLine($"  Ops/sec: {result.OperationsPerSecond:F2}");
                _output.WriteLine($"  Avg operation time: {result.AverageOperationTime.TotalMilliseconds:F2}ms");
                _output.WriteLine($"  P95 operation time: {result.P95OperationTime.TotalMilliseconds:F2}ms");

                if (result.Errors.Any())
                {
                    _output.WriteLine($"  Error types: {string.Join(", ", result.Errors.GroupBy(e => e.GetType().Name).Select(g => $"{g.Key}({g.Count()})"))}");
                }
            }
            finally
            {
                // Cleanup connections
                foreach (var connection in connections)
                {
                    try
                    {
                        await connection.DisposeAsync();
                    }
                    catch (Exception ex)
                    {
                        _output.WriteLine($"Error disposing connection: {ex.Message}");
                    }
                }
            }

            return result;
        }

        private async Task<HubConnection> CreateConnection(string roomId)
        {
            var connection = new HubConnectionBuilder()
                .WithUrl($"{_baseUrl}/roomHub")
                .Build();

            await connection.StartAsync();
            await connection.InvokeAsync("JoinRoom", roomId);
            
            return connection;
        }

        private async Task ExecuteRandomOperation(HubConnection connection, string roomId, int userIndex, int opIndex)
        {
            var random = new Random(userIndex * 1000 + opIndex);
            var operationType = random.Next(0, 4);

            switch (operationType)
            {
                case 0: // Pause
                    await connection.InvokeAsync("PauseSource", roomId, random.NextDouble() * 100);
                    break;
                case 1: // Play
                    await connection.InvokeAsync("PlaySource", roomId);
                    break;
                case 2: // Seek
                    await connection.InvokeAsync("SkipSourceTo", roomId, random.NextDouble() * 100, 0);
                    break;
                case 3: // Seek with keyboard
                    await connection.InvokeAsync("SkipSourceTo", roomId, random.NextDouble() * 100, random.Next(-10, 10));
                    break;
            }
        }
    }

    public class LoadTestConfig
    {
        public string RoomId { get; set; } = string.Empty;
        public int ConcurrentUsers { get; set; } = 10;
        public int OperationsPerUser { get; set; } = 20;
        public int DelayBetweenOperationsMs { get; set; } = 100;
    }

    public class LoadTestResult
    {
        public LoadTestConfig Config { get; set; } = new();
        public DateTime StartTime { get; set; }
        public DateTime EndTime { get; set; }
        public TimeSpan TotalDuration { get; set; }
        public int TotalOperations { get; set; }
        public int SuccessfulOperations { get; set; }
        public List<Exception> Errors { get; set; } = new();
        public List<TimeSpan> OperationTimes { get; set; } = new();
        public TimeSpan AverageOperationTime { get; set; }
        public TimeSpan MedianOperationTime { get; set; }
        public TimeSpan P95OperationTime { get; set; }
        public TimeSpan MaxOperationTime { get; set; }
        public double OperationsPerSecond { get; set; }
    }
}

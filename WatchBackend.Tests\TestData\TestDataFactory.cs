using WatchBackend.Core.Entities;
using WatchBackend.Core.Models;
using EntityPlaybackState = WatchBackend.Core.Entities.PlaybackState;
using ModelPlaybackState = WatchBackend.Core.Models.PlaybackState;

namespace WatchBackend.Tests.TestData
{
    public static class TestDataFactory
    {
        public static RoomEntity CreateRoomEntityWithPlaylist(int playlistItemCount = 10)
        {
            var room = new RoomEntity
            {
                Id = 1,
                IdHash = "test-room-hash",
                PlaybackState = EntityPlaybackState.Stopped,
                CurrentTime = TimeSpan.Zero,
                Users = new List<RoomUserEntity>(),
                Playlist = new SourceContainerPlaylistEntity
                {
                    Id = 1,
                    CurrentIndex = 0,
                    Items = new List<SourceContainerEntity>()
                }
            };

            // Create playlist items with varying complexity
            for (int i = 0; i < playlistItemCount; i++)
            {
                var sourceContainer = CreateSourceContainerEntity(i);
                room.Playlist.Items.Add(sourceContainer);
            }

            return room;
        }

        public static SourceContainerEntity CreateSourceContainerEntity(int index)
        {
            return new SourceContainerEntity
            {
                Id = index + 1,
                Type = "video",
                Video = new VideoSourceEntity
                {
                    Id = index + 1,
                    Url = $"https://example.com/video{index}.mp4",
                    Width = 1920,
                    Height = 1080,
                    Attributes = new Dictionary<string, string>
                    {
                        { "duration", "3600" },
                        { "bitrate", "5000" }
                    }
                },
                Subtitles = CreateSubtitleEntities(index),
                // ExternalInfo will be set as JSON string in real implementation
                Attributes = new Dictionary<string, string>
                {
                    { "source", "test" },
                    { "quality", "HD" }
                }
            };
        }

        private static ICollection<SubtitleSourceEntity> CreateSubtitleEntities(int index)
        {
            var subtitles = new List<SubtitleSourceEntity>();
            
            // Add multiple subtitle tracks to simulate real-world complexity
            var languages = new[] { "en", "es", "fr", "de", "ja" };
            for (int i = 0; i < Math.Min(3, languages.Length); i++)
            {
                subtitles.Add(new SubtitleSourceEntity
                {
                    Id = (index * 10) + i + 1,
                    Url = $"https://example.com/subtitles{index}_{languages[i]}.vtt",
                    Label = $"{languages[i].ToUpper()} Subtitles",
                    SrcLang = languages[i],
                    Offset = 0,
                    Attributes = new Dictionary<string, string>
                    {
                        { "format", "vtt" },
                        { "encoding", "utf-8" }
                    }
                });
            }

            return subtitles;
        }

        private static string CreateExternalInfoJson(int index)
        {
            // Simulate complex JSON external info that would cause serialization overhead
            var movieInfo = new
            {
                external_info_type = "movie",
                Title = $"Test Movie {index}",
                OriginalTitle = $"Original Test Movie {index}",
                Description = $"This is a test movie description for item {index}. " +
                             "It contains a lot of text to simulate real movie descriptions that can be quite long and detailed.",
                ReleaseDate = DateTime.Now.AddDays(-index * 30).ToString("yyyy-MM-dd"),
                PosterImageUrl = $"https://example.com/poster{index}.jpg",
                BackdropImageUrl = $"https://example.com/backdrop{index}.jpg",
                BackdropPlaceholderUrl = $"https://example.com/backdrop_placeholder{index}.jpg",
                ImdbId = $"tt{1000000 + index:D7}",
                OriginCountries = new[] { "US", "UK", "CA" },
                Genres = new[] { "Action", "Adventure", "Sci-Fi", "Thriller" },
                SpokenLanguages = new[] { "en", "es", "fr" },
                ProductionCompanies = new[]
                {
                    new { Name = $"Test Studios {index}", LogoImageUrl = $"https://example.com/studio{index}.png" },
                    new { Name = "Global Pictures", LogoImageUrl = "https://example.com/global.png" }
                },
                Budget = 50000000 + (index * 1000000),
                Tagline = $"The ultimate test movie experience {index}"
            };

            return System.Text.Json.JsonSerializer.Serialize(movieInfo);
        }

        public static Room CreateRoomWithPlaylist(int playlistItemCount = 10)
        {
            var room = new Room
            {
                Id = 1,
                IdHash = "test-room-hash",
                PlaybackState = ModelPlaybackState.Stopped,
                PlaybackBaseTime = TimeSpan.Zero
            };

            // Add test user
            var testUser = new RoomUser
            {
                Id = 1,
                UserId = 1,
                Username = "TestUser",
                Role = RoomUserRoles.Creator
            };
            room.AddUser(testUser);

            // Add playlist items
            for (int i = 0; i < playlistItemCount; i++)
            {
                var sourceContainer = CreateSourceContainer(i);
                room.Playlist.Add(sourceContainer);
            }

            if (playlistItemCount > 0)
            {
                room.Playlist.SetCurrentIndex(0);
            }

            return room;
        }

        private static SourceContainer CreateSourceContainer(int index)
        {
            return new SourceContainer
            {
                Id = index + 1,
                Type = "video",
                Video = new VideoSource
                {
                    Id = index + 1,
                    Url = $"https://example.com/video{index}.mp4",
                    Width = 1920,
                    Height = 1080,
                    Attributes = new Dictionary<string, string>
                    {
                        { "duration", "3600" },
                        { "bitrate", "5000" }
                    }
                },
                Subtitles = CreateSubtitleSources(index),
                Attributes = new Dictionary<string, string>
                {
                    { "source", "test" },
                    { "quality", "HD" }
                }
            };
        }

        private static IList<SubtitleSource> CreateSubtitleSources(int index)
        {
            var subtitles = new List<SubtitleSource>();
            var languages = new[] { "en", "es", "fr" };
            
            for (int i = 0; i < languages.Length; i++)
            {
                subtitles.Add(new SubtitleSource
                {
                    Id = (index * 10) + i + 1,
                    Url = $"https://example.com/subtitles{index}_{languages[i]}.vtt",
                    Label = $"{languages[i].ToUpper()} Subtitles",
                    SrcLang = languages[i],
                    Offset = 0,
                    Attributes = new Dictionary<string, string>
                    {
                        { "format", "vtt" },
                        { "encoding", "utf-8" }
                    }
                });
            }

            return subtitles;
        }
    }
}
